import re
import csv
import pdfplumber

# 1) Update these paths to match your files
PDF_PATH = "statement.pdf"
CSV_PATH = "recipients.csv"

# 2) Regex to catch:
#    • “Money Transfer – NAME – PHONE”
#    • “Raast Payment – NAME – PHONE”
pattern = re.compile(
    r'(?:Money Transfer|Raast Payment)\s*-\s*'       # transfer type
    r'(?P<name>[\w\s\.&]+?)\s*-\s*'                  # capture the name
    r'(?P<phone>\d{10,12})'                          # capture 10–12 digit phone
)

def extract_recipients(pdf_path):
    results = []
    with pdfplumber.open(pdf_path) as pdf:
        for page in pdf.pages:
            text = page.extract_text() or ""
            for match in pattern.finditer(text):
                name  = match.group("name").strip()
                phone = match.group("phone").strip()
                results.append((name, phone))
    return results

def save_to_csv(rows, csv_path):
    with open(csv_path, "w", newline="", encoding="utf-8") as f:
        writer = csv.writer(f)
        writer.writerow(["Name", "Phone"])
        writer.writerows(rows)

if __name__ == "__main__":
    recipients = extract_recipients(PDF_PATH)
    save_to_csv(recipients, CSV_PATH)
    print(f"Wrote {len(recipients)} entries to {CSV_PATH}")
    